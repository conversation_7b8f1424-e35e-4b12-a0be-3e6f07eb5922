import { FaAws } from "react-icons/fa6"
import {
  <PERSON><PERSON>s3,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ock<PERSON>,
  SiGit,
  SiHtml5,
  SiJavascript,
  SiJira,
  SiLinux,
  SiPostgresql,
  SiPostman,
  SiPython,
  SiReact,
  SiRedux,
  SiSass
} from "react-icons/si"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

const Skills = () => {
  const skills = [
    { name: "React", icon: SiReact, color: "#61DAFB" },
    { name: "JavaScript", icon: SiJavascript, color: "#F7DF1E" },
    { name: "Python", icon: SiPython, color: "#3776AB" },
    { name: "Django", icon: SiDjango, color: "#092E20" },
    { name: "Docker", icon: SiDocker, color: "#2496ED" },
    { name: "PostgreSQL", icon: SiPostgresql, color: "#336791" },
    { name: "<PERSON><PERSON>", icon: <PERSON>a<PERSON><PERSON>, color: "#FF9900" },
    { name: "Git", icon: SiGit, color: "#F05032" },
    { name: "HTML5", icon: SiHtml5, color: "#E34F26" },
    { name: "CSS3", icon: SiCss3, color: "#1572B6" },
    { name: "Sass", icon: SiSass, color: "#CC6699" },
    { name: "Redux", icon: SiRedux, color: "#764ABC" },
    { name: "Jira", icon: SiJira, color: "#0052CC" },
    { name: "Linux", icon: SiLinux, color: "#FCC624" },
    { name: "Postman", icon: SiPostman, color: "#FF6C37" }
  ]

  const itemVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  }

  return (
    <section id="skills" className="section">
      <div className="container mx-auto px-8">
        <div
          className="mb-12 text-center"
        >
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Skills & Technologies
          </h2>

          <p className="text-muted-foreground text-sm md:text-base">
            Technologies and tools I work with
          </p>
        </div>

        <div
          className="grid grid-cols-2 gap-4 px-16 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6"
        >
          {skills.map((skill) => {
            const IconComponent = skill.icon
            return (
              <div key={skill.name} variants={itemVariants}>
                <Card className="group relative overflow-hidden border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:bg-white/10 hover:shadow-lg hover:shadow-primary/25">
                  <CardContent className="flex flex-col items-center justify-center p-6">
                    <div
                    >
                      <IconComponent
                        className="mb-3 size-8 md:size-10"
                        style={{ color: skill.color }}
                      />
                    </div>
                    <Badge variant="skill" className="text-xs">
                      {skill.name}
                    </Badge>
                  </CardContent>

                  {/* Hover glow effect */}
                  <div className="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    <div
                      className="absolute inset-0 rounded-lg blur-xl"
                      style={{
                        background: `radial-gradient(circle at center, ${skill.color}20 0%, transparent 70%)`
                      }}
                    />
                  </div>
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default Skills
