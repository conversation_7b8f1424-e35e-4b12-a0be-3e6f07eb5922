[{"id": "01974ead-716b-722f-83f4-0559513a6023", "position": "Senior Software Developer", "company": "Alloi.ai", "duration": "June 2024 - Present", "location": "Remote, India", "type": "Full-time", "description": "Founding Team Member, Leading all development efforts, and driving innovation in the product of Alloi.", "technologies": ["React", "Python", "Django", "PostgreSQL", "TypeScript", "Tailwind CSS", "Zustand", "<PERSON>er", "AWS", "Git", "<PERSON><PERSON>", "Postman", "<PERSON><PERSON>"], "achievements": ["Helped build a scalable and secure backend for Alloi", "Developed and refined the UI, and Helped improve the UX", "Built AI Agent based workflow solutions for Product", "Lead team of 6 developers as a Founding Team Member"]}, {"id": "01974ead-ae5f-766c-b17b-45179fc9dfec", "position": "Senior Software Developer", "company": "Fero.ai", "duration": "September 2023 - June 2024", "location": "Ahmedabad, India", "type": "Full-time", "description": "Contributed in Transport Management SaaS Product as Full Stack Developer", "technologies": ["Python", "Django", "PostgreSQL", "Vue.js", "Redux", "JavaScript"], "achievements": ["Contributed in building features fast and efficiently", "Helped improve the overall Developer Experience by introducing processes such as linting, type-checking, and code reviews.", "Helped migrate the legacy frontend of the SaaS app from Vue 2 to Vue 3, and changed the bundler, resulting in an 80% decrease in Initial Page Load time, significantly enhancing the overall user experience."]}, {"id": "01974eae-0fde-724b-bccf-cdbf542b7f87", "position": "Software Developer 1", "company": "Aubergine Solutions", "duration": "April 2023 - September 2023", "location": "Ahmedabad, India", "type": "Full-time", "description": "Contributed as a Full Stack Developer in various client projects", "technologies": ["Python", "Django", "PostgreSQL", "React", "Redux", "JavaScript"], "achievements": ["Led a team of four, laying out the architecture and building the application with the best engineering practices.", "migrated AWS infrastructure to Azure, resulting in a significant reduction in operational costs.", "Implemented a CI/CD pipeline to automate the deployment process, ensuring a smooth and efficient deployment process."]}, {"id": "01974ec6-f293-727a-8e39-a721a508957b", "position": "Associate Software Engineer", "company": "Aubergine Solutions", "duration": "July 2021 - April 2023", "location": "Ahmedabad, India", "type": "Full-time", "description": "Contributed as a Full Stack Developer in various client projects", "technologies": ["Python", "Django", "PostgreSQL", "React", "Redux", "JavaScript", "<PERSON><PERSON>", "<PERSON>er"], "achievements": ["Built a complex data processing pipeline, including unit tests and automated data, which also facilitated the development of a performance testing program", "Improved this pipeline which resulted in a 30% increase in data processing speed."]}, {"id": "01974ecb-1025-7119-9436-e96b82494545", "position": "Software Developer Intern", "company": "Aubergine Solutions", "duration": "December 2020 - July 2021", "location": "Ahmedabad, India", "type": "Internship", "description": "Built mini-projects for the company's internal use", "technologies": ["Python", "Django", "PostgreSQL", "React", "TypeScript", "AWS"], "achievements": ["Built various Figma plugins to automate several tasks for Company's internal use", "Helped migrating Python lambda functions from Python 2 to Python 3.", "Helped building company's website using React and CMS"]}]