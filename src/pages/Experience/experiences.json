[{"id": "01974ead-716b-722f-83f4-0559513a6023", "position": "Senior Software Developer", "company": "Alloi.ai", "duration": "July 2024 - Present", "location": "Remote, India", "type": "Full-time", "description": "Founding Team Member, Leading all development efforts, and driving innovation in the product of Alloi.", "technologies": ["React", "Python", "Django", "PostgreSQL", "TypeScript", "Tailwind CSS", "Zustand", "<PERSON>er", "AWS", "Git", "<PERSON><PERSON>", "Postman", "<PERSON><PERSON>"], "achievements": ["Helped build a scalable and secure backend for Alloi", "Developed and refined the UI, and Helped improve the UX", "Built AI Agent based workflow solutions for Product", "Lead team of 6 developers as a Founding Team Member"]}, {"id": "01974ead-ae5f-766c-b17b-45179fc9dfec", "position": "Full Stack Developer", "company": "InnovateLab", "duration": "2021 - 2023", "location": "Austin, TX", "type": "Full-time", "description": "Developed and maintained multiple client-facing applications. Collaborated with cross-functional teams to deliver high-quality software solutions.", "technologies": ["React", "Python", "Django", "PostgreSQL", "Redis", "JavaScript"], "achievements": ["Built 3 major client applications from scratch", "Reduced bug reports by 35% through comprehensive testing", "Optimized database queries improving response time by 50%"]}]