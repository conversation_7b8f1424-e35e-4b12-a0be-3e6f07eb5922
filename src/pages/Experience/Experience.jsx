import {
  Building2,
  Calendar,
  ChevronRight,
  MapPin
} from "lucide-react"

import experiences from "./experiences.json"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"

const Experience = () => {

  return (
    <section id="experience" className="section">
      <div className="container mx-auto px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Experience
          </h2>
          <p className="text-sm text-muted-foreground md:text-base">
            My professional journey through the tech landscape
          </p>
        </div>

        <div className="relative mx-auto max-w-4xl">
          {/* Timeline line */}
          <div className="absolute inset-y-0 left-8 w-0.5 bg-gradient-to-b from-primary via-primary/70 to-primary opacity-30" />

          <div className="space-y-8">
            {experiences.map((experience) => (
              <div key={experience.id} className="relative">
                {/* Timeline dot */}
                <div className="border-transparent absolute left-6 top-6 h-4 w-4 rounded-full border-2 bg-gradient-to-r from-primary to-primary/80 shadow-lg" />

                <div className="ml-16">
                  <Card className="group border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:bg-white/10 hover:shadow-lg hover:shadow-primary/25">
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <CardTitle className="text-lg font-semibold text-white">
                            {experience.position}
                          </CardTitle>
                          <div className="text-muted-foreground flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <Building2 className="size-4" />
                              <span className="text-primary">
                                {experience.company}
                              </span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {experience.type}
                            </Badge>
                          </div>
                          <div className="text-muted-foreground flex items-center gap-4 text-xs">
                            <div className="flex items-center gap-1">
                              <Calendar className="size-3" />
                              {experience.duration}
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="size-3" />
                              {experience.location}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <CardDescription className="mb-4 text-sm text-gray-300">
                        {experience.description}
                      </CardDescription>

                      {/* Technologies */}
                      <div className="mb-4">
                        <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-400">
                          Technologies
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {experience.technologies.map((tech) => (
                            <Badge
                              key={tech}
                              variant="skill"
                              className="text-xs"
                            >
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Expandable achievements */}
                      <div className="border-t border-white/10 pt-4">
                        <Accordion type="single" collapsible className="w-full">
                          <AccordionItem value={`achievements-${experience.id}`} className="border-none">
                            <AccordionTrigger className="text-xs font-medium uppercase tracking-wide text-gray-400 hover:text-gray-300 hover:no-underline py-2 px-0">
                              Key Achievements
                            </AccordionTrigger>
                            <AccordionContent className="pt-2 pb-0">
                              <ul className="space-y-2">
                                {experience.achievements.map((achievement, idx) => (
                                  <li
                                    key={idx}
                                    className="flex items-start gap-2 text-sm text-gray-300"
                                  >
                                    <ChevronRight className="size-4 text-primary" />
                                    {achievement}
                                  </li>
                                ))}
                              </ul>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Experience
